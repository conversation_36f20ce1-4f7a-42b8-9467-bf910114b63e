<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';

  import { useVbenModal } from '@vben/common-ui';
  import { $t } from '@vben/locales';
  import { cloneDeep } from '@vben/utils';

  import { message } from 'ant-design-vue';

  import { useVbenForm } from '#/adapter/form';
  import {
    addScenarioEntry,
    fetchScenarioConditionConfig,
    updateScenarioEntry,
  } from '#/api/original-voucher/api-v2';
  import { useCompanySelection } from '#/components/ai-chat/composables/useCompanySelection';
  import { useSceneOptions } from '#/hooks/useSceneOptions';
  import { defaultFormValueGetter, useBeforeCloseDiff } from '#/utils/popup';

  import { modalSchema } from './data';

  const emit = defineEmits<{ reload: [] }>();

  // 使用公司选择功能
  const { companyList, fetchCompanyNames, selectedCompany } =
    useCompanySelection();

  const isUpdate = ref(false);

  // 场景配置数据
  const scenarioConfigData = ref<any[]>([]);

  const title = computed(() => {
    return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
  });

  const currentCompanyName = ref('');

  // 移除未使用的初始表单状态

  const [BasicForm, formApi] = useVbenForm({
    commonConfig: {
      // 通用配置项 会影响到所有表单项
      componentProps: {
        class: 'w-full',
      },
      // 默认占满一列
      formItemClass: 'col-span-1',
      // 默认label宽度 px
      labelWidth: 100,
    },
    schema: modalSchema(),
    showDefaultActions: false,
    wrapperClass: 'grid-cols-1',
  });

  const { markInitialized, onBeforeClose, resetInitialized } =
    useBeforeCloseDiff({
      currentGetter: defaultFormValueGetter(formApi),
      initializedGetter: defaultFormValueGetter(formApi),
    });

  // 使用场景选择hook
  const companyNameRef = {
    value: computed(() => currentCompanyName.value),
  };
  const { loading: loadingScenes, sceneOptions } =
    useSceneOptions(companyNameRef);

  // 监听场景选项变化，更新表单选项
  watch(
    sceneOptions,
    () => {
      if (formApi && formApi.updateSchema) {
        formApi.updateSchema([
          {
            componentProps: {
              loading: loadingScenes.value,
              options: sceneOptions.value,
            },
            fieldName: 'scene',
          },
        ]);
      }
    },
    { deep: true },
  );

  // 监听公司名称变化
  watch(
    () => selectedCompany.value,
    async (newValue) => {
      currentCompanyName.value = newValue || '';
      if (formApi.form) {
        await formApi.form.setFieldValue('company_name', newValue || '');
      }
    },
    { immediate: true },
  );

  // 监听表单值变化
  watch(
    () => formApi.form?.values?.company_name,
    async (newValue) => {
      if (newValue !== undefined) {
        currentCompanyName.value = newValue || '';
      }
    },
    { immediate: true },
  );

  const [BasicModal, modalApi] = useVbenModal({
    // 调整模态框宽度为更合适的尺寸
    class: 'w-[800px]',
    fullscreenButton: false,
    onBeforeClose,
    onClosed: handleClosed,
    onConfirm: handleConfirm,
    onOpenChange: async (isOpen) => {
      if (!isOpen) {
        return null;
      }
      modalApi.modalLoading(true);

      const { record } = modalApi.getData() as { record?: any };
      isUpdate.value = !!record;

      // 确保公司数据和场景配置已加载
      await fetchCompanyNames();
      await fetchScenarioConfig();
      updateModalCompanyOptions(); // 这里会根据 isUpdate.value 设置禁用状态

      if (isUpdate.value && record) {
        // 直接设置表单数据，分录数据保持数组格式
        const formData = { ...record };
        // 确保状态字段是字符串格式
        if (typeof formData.status === 'number') {
          formData.status = String(formData.status);
        }

        await formApi.setValues(formData);
        console.log('📝 编辑模式设置表单数据完成，类型:', formData.type);
      } else {
        // 新增时设置默认值
        await formApi.setValues({
          detail: [], // 默认空数组
          status: '1', // 默认启用
        });
      }
      await markInitialized();

      modalApi.modalLoading(false);
    },
  });

  // 获取场景配置数据
  async function fetchScenarioConfig() {
    try {
      const result = await fetchScenarioConditionConfig({
        config_type: 'scene_condition',
      });
      if (result.success) {
        scenarioConfigData.value = result.data;
        updateTypeOptions();
        console.log('✅ 场景配置数据已加载:', result.data);
      } else {
        console.error('❌ 获取场景配置失败:', result.message);
      }
    } catch (error) {
      console.error('❌ 获取场景配置异常:', error);
    }
  }

  // 更新类型选项
  function updateTypeOptions() {
    const typeOptions = scenarioConfigData.value.map((item) => ({
      label: item.config_key,
      value: item.config_key,
    }));

    if (formApi && formApi.updateSchema) {
      formApi.updateSchema([
        {
          componentProps: {
            onChange: handleTypeChange,
            options: typeOptions,
          },
          fieldName: 'type',
        },
      ]);
      console.log('✅ 类型选项已更新:', typeOptions);
    }
  }

  // 处理类型变化
  function handleTypeChange(value: string) {
    console.log('🎯 主表单类型变化:', value);
    // dependencies会自动处理selectedType的传递，这里不需要手动更新
  }

  // 移除未使用的函数

  // 初始化公司数据和场景配置
  onMounted(async () => {
    await fetchCompanyNames();
    await fetchScenarioConfig();
    updateModalCompanyOptions();
  });

  // 监听公司列表变化，更新表单选项
  watch(
    companyList,
    () => {
      updateModalCompanyOptions();
    },
    { deep: true },
  );

  // 处理模态框中的公司选择变化
  function handleModalCompanyChange(value: string) {
    console.log('🔄 模态框公司选择变化:', value);
    // 更新全局选中的公司
    selectedCompany.value = value;
  }

  // 更新模态框表单中的公司选项
  function updateModalCompanyOptions() {
    const companyOptions = companyList.value.map((company) => ({
      label: company.name,
      value: company.name,
    }));

    console.log('🏢 模态框更新公司选项:', {
      公司列表: companyOptions.map((c) => c.label),
      公司数量: companyOptions.length,
    });

    // 使用 formApi 的 updateSchema 方法来更新字段配置
    if (formApi && formApi.updateSchema) {
      formApi.updateSchema([
        {
          componentProps: {
            disabled: isUpdate.value, // 编辑模式下禁用公司选择
            onChange: handleModalCompanyChange, // 添加变化处理
            options: companyOptions,
          },
          fieldName: 'company_name',
        },
        {
          componentProps: {
            disabled: isUpdate.value, // 编辑模式下禁用场景输入
          },
          fieldName: 'scene',
        },
      ]);
      console.log('✅ 模态框公司选项已更新');

      // 设置默认选中的公司
      const currentSelected = selectedCompany.value;
      if (currentSelected && formApi.setFieldValue && !isUpdate.value) {
        formApi.setFieldValue('company_name', currentSelected);
        console.log('🎯 模态框设置默认选中公司:', currentSelected);
      }
    } else {
      console.warn('⚠️ 模态框 formApi 不可用');
    }
  }

  async function handleConfirm() {
    try {
      modalApi.lock(true);
      const { valid } = await formApi.validate();
      if (!valid) {
        return;
      }

      // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
      const data = cloneDeep(await formApi.getValues());

      // 处理分录数据，确保是数组格式
      if (data.detail && Array.isArray(data.detail)) {
        // 验证分录数据格式
        for (const item of data.detail) {
          if (!item.subject || !item.direction || !item.source) {
            message.error('每个分录必须包含科目、方向和来源');
            return;
          }
          if (!['借', '贷'].includes(item.direction)) {
            message.error('分录方向必须是"借"或"贷"');
            return;
          }
        }
      } else {
        data.detail = [];
      }

      if (isUpdate.value) {
        await updateScenarioEntry(data as any);
        message.success('更新成功');
      } else {
        await addScenarioEntry(data as any);
        message.success('添加成功');
      }

      resetInitialized();
      emit('reload');
      modalApi.close();
    } catch (error) {
      console.error(error);
      message.error('操作失败，请重试');
    } finally {
      modalApi.lock(false);
    }
  }

  async function handleClosed() {
    await formApi.resetForm();
    resetInitialized();
  }
</script>

<template>
  <BasicModal :title="title">
    <BasicForm />
  </BasicModal>
</template>
